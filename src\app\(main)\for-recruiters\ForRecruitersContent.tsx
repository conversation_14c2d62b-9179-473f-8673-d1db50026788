"use client";

import dynamic from "next/dynamic";
import { useSearch<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useState } from "react";
import { Suspense } from "react";

import { BannerHeading } from "@/components/Headings/BannerHeading";
import SearchBar from "@/components/Sections/SearchBar";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { useGetAllJobSeekers } from "@/hooks/useQuery";
import { useUserStore } from "@/store/useUserStore";
import type { IJobSeeker, IJobSeekerSearchParams } from "@/types/query.types";

// Import CandidateCard with dynamic import to prevent hydration errors
const CandidateCard = dynamic(() => import("@/components/Cards/CandidateCard"), {
  ssr: false,
  loading: () => (
    <div className="p-6 border border-gray-300 rounded-2xl shadow-md h-[400px] animate-pulse bg-gray-100"></div>
  ),
});

export default function ForRecruitersContent() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Initialize search state from URL parameters
  const [searchState, setSearchState] = useState<IJobSeekerSearchParams>({
    search: searchParams.get("search") || "",
    page: searchParams.get("page") ? parseInt(searchParams.get("page")!) : 1,
    limit: searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : 10,
    coordinates: searchParams.get("coordinates")
      ? (searchParams.get("coordinates")!.split(",").map(Number) as [number, number])
      : undefined,
  });

  // Fetch job seekers data
  const { data, isLoading, isError } = useGetAllJobSeekers(searchState);
  const jobSeekers = data?.data.allJobSeekers || [];
  const pagination = data?.data.pagination;

  // Handle search submission
  const handleSearch = (params: { search: string; coordinates?: [number, number] }) => {
    // If search is empty and no coordinates, clear all search filters
    if (!params.search && !params.coordinates) {
      const newSearchState = {
        ...searchState,
        search: undefined,
        coordinates: undefined,
        page: 1, // Reset to first page on new search
      };
      setSearchState(newSearchState);
      updateUrl(newSearchState);
      return;
    }

    const newSearchState = {
      ...searchState,
      search: params.search || undefined, // Use undefined instead of empty string
      coordinates: params.coordinates,
      page: 1, // Reset to first page on new search
    };
    setSearchState(newSearchState);
    updateUrl(newSearchState);
  };

  // Handle per page change
  const handlePerPageChange = (limit: number) => {
    const newSearchState = {
      ...searchState,
      limit,
      page: 1, // Reset to first page when changing items per page
    };
    setSearchState(newSearchState);
    updateUrl(newSearchState);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    const newSearchState = {
      ...searchState,
      page,
    };
    setSearchState(newSearchState);
    updateUrl(newSearchState);
  };

  // Update URL with search parameters
  const updateUrl = (params: IJobSeekerSearchParams) => {
    const urlParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value) {
        if (key === "coordinates" && Array.isArray(value)) {
          urlParams.set(key, value.join(","));
        } else {
          urlParams.set(key, String(value));
        }
      }
    });

    router.push(`/for-recruiters${urlParams.toString() ? `?${urlParams.toString()}` : ""}`, {
      scroll: false,
    });
  };

  const { currentUser } = useUserStore();

  return (
    <>
      <section className="bg-offWhite-100 py-20">
        <div className="container mx-auto">
          <div className="mb-10">
            <BannerHeading>
              Find the Talent That <span>Drives Success</span>
            </BannerHeading>
          </div>
          <div className="mb-10">
            <SearchBar onSearch={handleSearch} initialSearch={searchState.search || ""} />
          </div>
        </div>
      </section>
      <section className="pt-16 pb-20">
        <div className="container mx-auto">
          <div className="flex gap-6 items-center">
            <div>
              {pagination && (
                <p className="text-[#000]">
                  Showing {(pagination.currentPage - 1) * pagination.pageSize + 1} -{" "}
                  {Math.min(pagination.currentPage * pagination.pageSize, pagination.totalRecords)}{" "}
                  of {pagination.totalRecords} results
                </p>
              )}
            </div>
            {/* <div className="ml-auto">
              <Select>
                <SelectTrigger className="w-full bg-white rounded-full h-[56px] px-6 text-gray-100 text-base">
                  <SelectValue placeholder="Sort by (Default)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="default">Sort by (Default)</SelectItem>
                    <SelectItem value="name_asc">Name (A-Z)</SelectItem>
                    <SelectItem value="name_desc">Name (Z-A)</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div> */}
            <div>
              <Select
                value={searchState.limit?.toString() || "10"}
                onValueChange={(value) => handlePerPageChange(parseInt(value))}
              >
                <SelectTrigger className="w-full bg-white rounded-full h-[56px] px-6 text-gray-100 text-base">
                  <SelectValue placeholder={`${searchState.limit || 10} Per Page`} />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="10">10 Per Page</SelectItem>
                    <SelectItem value="20">20 Per Page</SelectItem>
                    <SelectItem value="30">30 Per Page</SelectItem>
                    <SelectItem value="40">40 Per Page</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-100"></div>
            </div>
          ) : isError ? (
            <div className="text-center py-8 text-red-500">
              Error loading candidates. Please try again later.
            </div>
          ) : jobSeekers.length === 0 ? (
            <div className="text-center py-8">No candidates found matching your criteria.</div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mt-10">
              {jobSeekers.map((jobSeeker) => (
                <div key={jobSeeker._id}>
                  <Suspense fallback={<div>Loading...</div>}>
                    <CandidateCard
                      candidateId={jobSeeker._id}
                      candidateDescription={jobSeeker.userProfile.shortBio || "No bio provided."}
                      candidateImage={jobSeeker.userProfile.profilePicture || DEFAULT_IMAGE}
                      candidateLocation={jobSeeker.userProfile.location?.city || ""}
                      candidateName={`${jobSeeker.userProfile.firstName} ${jobSeeker.userProfile.lastName}`}
                      candidateProfessional={jobSeeker.userProfile.designation || "Not specified"}
                      candidateSalary={formatSalaryRange(jobSeeker)}
                      candidateSkills={jobSeeker.skills}
                      link={currentUser ? `/for-recruiters/${jobSeeker._id}` : `/login`}
                      isSaved={jobSeeker.isSaved}
                      proMembershipEndsAt={jobSeeker.userProfile.proMembershipEndsAt}
                      proTrialEndsAt={jobSeeker.userProfile.proTrialEndsAt}
                    />
                  </Suspense>
                </div>
              ))}
            </div>
          )}

          {pagination && pagination.pages > 1 && (
            <div className="flex justify-center mt-10">
              <div className="flex gap-2">
                <button
                  onClick={() => handlePageChange(pagination.currentPage - 1)}
                  disabled={pagination.currentPage === 1}
                  className={`border rounded-full w-10 h-10 flex items-center justify-center ${pagination.currentPage === 1 ? "border-gray-300 text-gray-300" : "border-orange-100 text-orange-100 hover:bg-orange-100 hover:text-white"}`}
                >
                  &lt;
                </button>

                {Array.from({ length: pagination.pages }, (_, i) => i + 1).map((page) => (
                  <button
                    key={page}
                    onClick={() => handlePageChange(page)}
                    className={`border rounded-full w-10 h-10 flex items-center justify-center ${pagination.currentPage === page ? "bg-orange-100 text-white" : "border-orange-100 text-orange-100 hover:bg-orange-100 hover:text-white"}`}
                  >
                    {page}
                  </button>
                ))}

                <button
                  onClick={() => handlePageChange(pagination.currentPage + 1)}
                  disabled={pagination.currentPage === pagination.pages}
                  className={`border rounded-full w-10 h-10 flex items-center justify-center ${pagination.currentPage === pagination.pages ? "border-gray-300 text-gray-300" : "border-orange-100 text-orange-100 hover:bg-orange-100 hover:text-white"}`}
                >
                  &gt;
                </button>
              </div>
            </div>
          )}
        </div>
      </section>
    </>
  );
}

// Helper function to format salary range - avoiding toLocaleString() to prevent hydration errors
function formatSalaryRange(jobSeeker: IJobSeeker) {
  if (!jobSeeker.jobPreferences.salaryRangeStart && !jobSeeker.jobPreferences.salaryRangeEnd) {
    return "Not specified";
  }

  // Format numbers without using toLocaleString to ensure server/client consistency
  const formatNumber = (num: number) => {
    return `$${num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")}`;
  };

  const start = jobSeeker.jobPreferences.salaryRangeStart
    ? formatNumber(jobSeeker.jobPreferences.salaryRangeStart)
    : "";

  const end = jobSeeker.jobPreferences.salaryRangeEnd
    ? formatNumber(jobSeeker.jobPreferences.salaryRangeEnd)
    : "";

  return start && end ? `${start} - ${end}` : start || end;
}
