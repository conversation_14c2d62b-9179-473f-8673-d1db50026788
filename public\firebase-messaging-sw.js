importScripts("https://www.gstatic.com/firebasejs/10.12.1/firebase-app-compat.js");
importScripts("https://www.gstatic.com/firebasejs/10.12.1/firebase-messaging-compat.js");

firebase.initializeApp({
  apiKey: "AIzaSyAYZGXENJgvpkbl5frj2GHx9Lm4Rmvz7U0",
  authDomain: "yes-jobs-7468e.firebaseapp.com",
  projectId: "yes-jobs-7468e",
  storageBucket: "yes-jobs-7468e.firebasestorage.app",
  messagingSenderId: "578851155323",
  appId: "1:578851155323:web:e884470770aa57b4e1e1de",
  measurementId: "G-4J1FTM5Q7Y",
});

const messaging = firebase.messaging();

messaging.onBackgroundMessage((payload) => {
  console.log("[firebase-messaging-sw.js] Received background message", payload);

  const notificationTitle = payload.notification?.title || "Notification";
  const notificationOptions = {
    body: payload.notification?.body,
    icon: "/images/logo.png", // optional
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});
