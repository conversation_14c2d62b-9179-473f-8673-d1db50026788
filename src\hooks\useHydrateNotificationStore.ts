import { useEffect, useState } from "react";
import { useNotificationStore } from "@/store/useNotificationStore";

/**
 * Hook to hydrate the notification store from localStorage
 * This ensures the store is properly initialized before using it
 */
export const useHydrateNotificationStore = () => {
  const [isHydrated, setIsHydrated] = useState(false);
  const { hasHydrated, setHydrated } = useNotificationStore();

  useEffect(() => {
    // Wait for the store to be hydrated from localStorage
    const unsubscribe = useNotificationStore.persist.onFinishHydration(() => {
      setHydrated();
      setIsHydrated(true);
    });

    // If already hydrated, set the state immediately
    if (hasHydrated) {
      setIsHydrated(true);
    }

    return unsubscribe;
  }, [hasHydrated, setHydrated]);

  return isHydrated;
};
