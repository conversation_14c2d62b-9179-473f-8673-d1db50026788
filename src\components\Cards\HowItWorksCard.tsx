import Image from "next/image";
import React from "react";
import "tailwindcss/tailwind.css";
import { YesJobIcon } from "../Icons";

interface HowItWorksCardProps {
  icon: string;
  title: string;
  description: string;
}

const HowItWorksCard: React.FC<HowItWorksCardProps> = ({ icon, title, description }) => {
  return (
    <div className="border text-orange-100 border-gray-300 rounded-[30px] text-left p-8 how-it-works-card h-full relative">
      <div className="absolute top-0 right-0 ">
        <YesJobIcon />
      </div>
      <div className="mb-4">
        <Image src={icon} alt={title} width={40} height={40} />
      </div>
      <h3 className="font-bold text-2xl text-black-100 mb-4">{title}</h3>
      <p className="text-base text-gray-100 ">{description}</p>
    </div>
  );
};

export default HowItWorksCard;
