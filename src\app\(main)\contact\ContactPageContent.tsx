"use client";

import Image from "next/image";
import ContactForm from "../../../components/forms/ContactForm";
import ContactCard from "@/components/Cards/ContactCard";
import { PrimaryHeading } from "@/components/Headings/PrimaryHeading";
import { EmailIcon, MapTrifold } from "@/components/Icons";
import CTASection from "@/components/Sections/CTASection";
import MobileAppSection from "@/components/Sections/MobileAppSection";
import { useGetContactUsPage } from "@/hooks/useQuery";

const ContactPageContent = () => {
  const { data: contactUsPageData } = useGetContactUsPage();
  return (
    <>
      <section className="lg:pt-36 pt-14 lg:pb-20 pb-14 relative">
        <div className="absolute w-full left-0 z-[-1]">
          <Image alt="" src={"/images/spiral.png"} width={837} height={1920} className="w-full" />
        </div>
        <div className="container mx-auto">
          <div className="grid md:grid-cols-2 grid-cols-1 gap-x-16 gap-y-6">
            <div>
              <PrimaryHeading>
                <span> {contactUsPageData?.data.page.heading}</span>
              </PrimaryHeading>
              <p className="text-lg font-normal text-gray-100 mt-3 mb-6">
                {contactUsPageData?.data.page.description}
              </p>
              <ContactForm />
            </div>

            <div>
              <Image
                src={contactUsPageData?.data.page.image || "/images/contact-main.png"}
                alt="contact"
                width={720}
                height={798}
              />
            </div>
          </div>
        </div>
      </section>
      <section className="mb-20">
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-10 mb-20">
            <ContactCard description="<EMAIL>" icon={<EmailIcon />} title="Email" />
            {/* <ContactCard
              description="******-YES-JOBS"
              icon={<HeadsetIcon />}
              title="Phone:"
            /> */}
            <ContactCard
              description="The address is The address is suite2
Level 13
977 ann street"
              icon={<MapTrifold />}
              title="Address"
            />
          </div>
          <Image src={"/images/fgmap.png"} alt="" width={1520} height={750} />
        </div>
      </section>
      <CTASection />
      <MobileAppSection />
    </>
  );
};

export default ContactPageContent;
