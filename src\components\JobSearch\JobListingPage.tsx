"use client";

import { useQueryClient } from "@tanstack/react-query";
import { useRouter, useSearchParams } from "next/navigation";
import { useState, useEffect, useCallback, useMemo, Suspense } from "react";

import { Button } from "../ui/button";
import { FilterSelects } from "./FilterSelects";
import { Pagination } from "./Pagination";
import { ResultsPerPage } from "./ResultsPerPage";
import SearchBar from "./SearchBar";
import JobDetail from "@/components/Cards/JobDetail";
import JobShortCard from "@/components/Cards/JobShortCard";
import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { useGetAllJobs } from "@/hooks/useQuery";
import { formatDate } from "@/lib/utils";
import type { IAllJob, IJobSearchParams } from "@/types/query.types";

// Local imports

// Define interface for filter state including jobId
interface IJobFilterState extends IJobSearchParams {
  jobId?: string;
}

export default function JobListingPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  // Initialize state from URL parameters
  const [selectedJobId, setSelectedJobId] = useState<string | null>(searchParams.get("jobId"));

  // Add pendingFilters state to store filter changes before applying
  const [pendingFilters, setPendingFilters] = useState<Partial<IJobSearchParams>>({});

  const [searchState, setSearchState] = useState<IJobSearchParams>(() => {
    // Parse coordinates if they exist in URL - using a safer approach
    const coordinates = searchParams.get("coordinates")
      ? (() => {
          const coordsStr = searchParams.get("coordinates") || "";
          // Simple comma-separated format only for consistency
          const parts = coordsStr.split(",") as string[];
          if (parts.length === 2) {
            const lng = Number.parseFloat(parts[0] || "0");
            const lat = Number.parseFloat(parts[1] || "0");
            if (!isNaN(lng) && !isNaN(lat)) {
              return [lng, lat] as [number, number];
            }
          }
          return undefined;
        })()
      : undefined;

    return {
      search: searchParams.get("search") || "",
      page: Number.parseInt(searchParams.get("page") || "1"),
      limit: Number.parseInt(searchParams.get("limit") || "10"),
      jobType: searchParams.get("jobType") || undefined,
      experienceLevel: searchParams.get("experienceLevel") || undefined,
      qualification: searchParams.get("qualification") || undefined,
      careerLevel: searchParams.get("careerLevel") || undefined,
      salaryType: searchParams.get("salaryType") || undefined,
      coordinates,
      // Always add maxDistance: 50 when coordinates are provided
      maxDistance: coordinates ? 50 : undefined,
      // Note: jobId is not included here, as we don't want it to affect the API call
    };
  });

  const { data, isLoading, isError } = useGetAllJobs(searchState);

  // Use useMemo to avoid recreating the jobs array on every render
  const jobs = useMemo(() => data?.data.allJobs || [], [data?.data.allJobs]);
  const pagination = data?.data.pagination;

  // Use useCallback for updateUrl function to avoid recreating it on every render
  const updateUrl = useCallback(
    (params: IJobFilterState) => {
      const urlParams = new URLSearchParams();
      const { jobId, ...searchParamsData } = params;

      Object.entries(searchParamsData).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          if (key === "coordinates" && Array.isArray(value)) {
            // Format coordinates as comma-separated string for consistency
            urlParams.set(key, value.join(","));
          } else {
            urlParams.set(key, String(value));
          }
        }
      });

      // Add jobId to URL params but don't include it in the search state
      if (jobId) {
        urlParams.set("jobId", jobId);
      }

      // Add location parameter if it exists in the original URL
      const locationParam = searchParams.get("location");
      if (locationParam) {
        urlParams.set("location", locationParam);
      }

      const url = urlParams.toString() ? `?${urlParams.toString()}` : "";
      router.push(`/for-candidates${url}`, { scroll: false });
    },
    [router, searchParams]
  );

  // Handle immediate search updates
  const handleSearch = (params: { search: string; coordinates?: [number, number] }) => {
    // Ensure coordinates are properly formatted
    const formattedParams = { ...params };

    // Ensure coordinates are valid numbers
    if (formattedParams.coordinates) {
      formattedParams.coordinates = [
        Number(formattedParams.coordinates[0]),
        Number(formattedParams.coordinates[1]),
      ] as [number, number];
    }

    const newParams = {
      ...searchState,
      ...formattedParams,
      page: 1,
      // Always add maxDistance: 50 when coordinates are provided
      maxDistance: formattedParams.coordinates ? 50 : undefined,
    };

    setSearchState(newParams);
    updateUrl(newParams);
  };

  // Handle filter changes (stored in pending state)
  const handleFilterChange = (key: keyof IJobSearchParams, value: string | undefined) => {
    setPendingFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // Apply filters
  const handleApplyFilters = () => {
    const newParams = {
      ...searchState,
      ...pendingFilters,
      page: 1,
    };
    setSearchState(newParams);
    updateUrl(newParams);
  };

  // Clear filters
  const handleClearFilters = () => {
    setPendingFilters({});
    const newParams = {
      search: searchState.search,
      coordinates: searchState.coordinates,
      maxDistance: searchState.coordinates ? 50 : undefined,
      page: 1,
    };
    setSearchState(newParams);
    updateUrl(newParams);
  };

  // Clear all search parameters
  const handleClearAllSearchParams = () => {
    setPendingFilters({});
    const newParams = {
      page: 1,
      limit: searchState.limit,
    };
    setSearchState(newParams);
    updateUrl(newParams);
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    const newParams = {
      ...searchState,
      page,
    };
    setSearchState(newParams);
    updateUrl(newParams);
  };

  // Handle limit change
  const handleLimitChange = (limit: number) => {
    const newParams = {
      ...searchState,
      limit,
      page: 1, // Reset to first page when changing limit
    };
    setSearchState(newParams);
    updateUrl(newParams);
  };

  // Handle job selection
  const handleJobSelect = (jobId: string) => {
    setSelectedJobId(jobId);
    // Only update URL with jobId, don't include it in searchState
    updateUrl({ ...searchState, jobId });
  };

  // Set the first job as selected when jobs are loaded
  useEffect(() => {
    if (jobs.length > 0 && !selectedJobId && jobs[0]?._id) {
      setSelectedJobId(jobs[0]._id);
      // Only update URL with jobId, don't include it in searchState
      updateUrl({ ...searchState, jobId: jobs[0]._id });
    }
  }, [jobs, selectedJobId, searchState, updateUrl]);

  const selectedJob = jobs.find((job) => job._id === selectedJobId);

  // Format salary range for display
  const formatSalaryRange = (job: IAllJob) => {
    const currency = "$";
    const start = job.salaryRangeStart.toLocaleString();
    const end = job.salaryRangeEnd.toLocaleString();

    if (job.salaryType === "ANNUAL") {
      return `${currency}${start} - ${currency}${end} per year`;
    } else if (job.salaryType === "MONTHLY") {
      return `${currency}${start} - ${currency}${end} per month`;
    } else if (job.salaryType === "HOURLY") {
      return `${currency}${start} - ${currency}${end} per hour`;
    } else {
      return `${currency}${start} - ${currency}${end}`;
    }
  };

  // Parse responsibilities and skills from string to array
  const parseTextToArray = (text: string): string[] => {
    if (!text) return [];

    // Split by semicolons, commas, or newlines
    const items = text.split(/[;\n]/).filter((item) => item.trim().length > 0);

    if (items.length === 1 && items[0] && !items[0].includes(";") && !items[0].includes("\n")) {
      // If only one item and no delimiters, try splitting by periods or commas
      return text
        .split(/[.,]/)
        .filter((item) => item.trim().length > 0)
        .map((item) => item.trim());
    }

    return items.map((item) => item.trim());
  };

  if (isLoading) {
    return <div className="flex justify-center items-center min-h-[400px]">Loading jobs...</div>;
  }

  if (isError) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        Error loading jobs. Please try again.
      </div>
    );
  }

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <section className="bg-offWhite-100 py-20">
        <div className="container mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-6">Find Your Dream Job</h1>
            <SearchBar
              onSearch={handleSearch}
              initialSearch={searchState.search || ""}
              initialLocation={searchParams.get("location") || ""}
            />
            <div className="mt-4 w-full">
              <FilterSelects filters={pendingFilters} onFilterChange={handleFilterChange} />
              <div className="mt-4 flex gap-4 justify-end ">
                <Button onClick={handleApplyFilters}>Apply Filters</Button>
                <Button variant="outline" onClick={handleClearFilters}>
                  Clear Filters
                </Button>
                <Button variant="outline" onClick={handleClearAllSearchParams}>
                  Clear All Search
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section className="pt-16 pb-20">
        <div className="container mx-auto">
          <div className="lg:flex h-full">
            {/* Left side - Job listings */}
            <div className="lg:w-[35%] space-y-5 h-full">
              {/* Results per page shown before job listings */}
              {pagination && (
                <ResultsPerPage pagination={pagination} onLimitChange={handleLimitChange} />
              )}
              {jobs.length === 0 ? (
                <div className="p-8 text-center border rounded-lg">
                  <p className="text-gray-500">No jobs found matching your criteria.</p>
                  <p className="mt-2">Try adjusting your search or filters.</p>
                </div>
              ) : (
                <div className="space-y-6 p-1">
                  {jobs.map((job) => (
                    <div
                      key={job._id}
                      onClick={() => handleJobSelect(job._id)}
                      className={`cursor-pointer transition-all ${selectedJobId === job._id ? "ring-2 ring-orange-100 rounded-[18px]" : ""}`}
                    >
                      <JobShortCard
                        _jobId={job._id}
                        imageUrl={
                          job.recruiterProfile.companyProfile.profilePicture || DEFAULT_IMAGE
                        }
                        jobTitle={job.jobTitle}
                        companyName={job.recruiterProfile.companyProfile.companyName}
                        category={job.jobCategory.replace(/_/g, " ")}
                        jobType={job.jobType.replace(/_/g, " ")}
                        cityName={job.location.city}
                        salaryRange={formatSalaryRange(job)}
                        deadline={formatDate(job.applicationDeadline)}
                        isSaved={job.isSaved}
                        premiumExpireAt={job.premiumExpireAt}
                      />
                    </div>
                  ))}
                </div>
              )}
              {/* Pagination controls shown after job listings */}
              {pagination && jobs.length > 0 && (
                <Pagination
                  pagination={pagination}
                  onPageChange={handlePageChange}
                  onLimitChange={handleLimitChange}
                />
              )}
            </div>

            {/* Right side - Job details */}
            <div className="lg:w-[67%] xl:ml-10 ml-5 hidden lg:block sticky top-24 h-full">
              {selectedJob ? (
                <JobDetail
                  jobId={selectedJob._id}
                  imageUrl={
                    selectedJob.recruiterProfile.companyProfile.profilePicture || DEFAULT_IMAGE
                  }
                  jobTitle={selectedJob.jobTitle}
                  companyName={selectedJob.recruiterProfile.companyProfile.companyName}
                  category={selectedJob.jobCategory.replace(/_/g, " ")}
                  jobType={selectedJob.jobType.replace(/_/g, " ")}
                  cityName={selectedJob.location.city}
                  salaryRange={formatSalaryRange(selectedJob)}
                  deadline={formatDate(selectedJob.applicationDeadline)}
                  datePosted={formatDate(selectedJob.createdAt)}
                  location={selectedJob.location.formattedAddress}
                  experience={selectedJob.experienceLevel.replace(/_/g, " ")}
                  qualification={selectedJob.qualification.replace(/_/g, " ")}
                  careerLevel={selectedJob.careerLevel.replace(/_/g, " ")}
                  jobDescription={selectedJob.jobDescription}
                  keyResponsibilities={parseTextToArray(selectedJob.keyResponsibilities)}
                  skillsExperience={parseTextToArray(selectedJob.skillsAndExperience)}
                  skillsTags={selectedJob.skillsTag || []}
                  isSaved={selectedJob.isSaved}
                  premiumExpireAt={selectedJob.premiumExpireAt}
                  onApplySuccess={() => {
                    // Refresh the job list after successful application
                    queryClient.invalidateQueries({ queryKey: ["get-all-jobs"] });
                  }}
                  alreadyApplied={selectedJob.alreadyApplied}
                  isJobDetailWeb={true}
                />
              ) : (
                <div className="p-8 text-center border rounded-lg">
                  <p className="text-gray-500">Select a job to view details</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>
    </Suspense>
  );
}
