"use client";

import { useQ<PERSON>yClient } from "@tanstack/react-query";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState, useMemo } from "react";
import { toast } from "sonner";
import { BellIconNotification, CrownIcon, MessageIcon } from "../Icons";
import { Button } from "../ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { NavLink } from "./NavLink";
import FullPageLoader from "@/components/ui/FullPageLoader";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { useLoader } from "@/context/LoaderContext";
import { useLogout, usePurchasePro, useMarkAllNotificationsAsRead } from "@/hooks/useMutation";
import {
  useGetConversations,
  useGetAdminSettings,
  useGetNotifications,
  useGetUnreadNotificationsCount,
} from "@/hooks/useQuery";
import { useGetCurrentUser } from "@/hooks/useQuery";
import { removeFirebaseToken } from "@/service/mutation.service";
import {
  markNotificationAsReadSocket,
  markAllNotificationsAsReadSocket,
} from "@/service/socket.service";
import { useNotificationStore } from "@/store/useNotificationStore";
import { useUserStore } from "@/store/useUserStore";
import { ApiError, UserRole } from "@/types/common.types";
import { IPurchaseProResponseDto } from "@/types/mutation.types";
import { CurrentCompanyType, CurrentJobSeekerType } from "@/types/query.types";

const Header = ({ dashboardPages = false }: { dashboardPages?: boolean }) => {
  const { currentUser, hasHydrated, logout: clearStore } = useUserStore();
  const { showLoader, hideLoader } = useLoader();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [unreadMessageCount, setUnreadMessageCount] = useState(0);
  const [isPurchaseProModalOpen, setIsPurchaseProModalOpen] = useState(false);
  const [selectedWeeks, setSelectedWeeks] = useState(1); // Default to 4 weeks as per request body
  const [isNotificationDropdownOpen, setIsNotificationDropdownOpen] = useState(false);
  const router = useRouter();
  const queryClient = useQueryClient();

  const { data: adminSettingsData } = useGetAdminSettings();
  const proMemberPricePerWeek = adminSettingsData?.data?.ProMemberPricePerWeek || 0;

  const { mutate: purchasePro, isPending: isPurchasingPro } = usePurchasePro();

  const purchaseProPrice = useMemo(() => {
    return proMemberPricePerWeek * selectedWeeks;
  }, [proMemberPricePerWeek, selectedWeeks]);

  // Move all hook calls before any conditional returns
  const { data: userData } = useGetCurrentUser({
    enabled: !!currentUser && hasHydrated,
  });

  // Get conversations to count unread messages
  const { data: conversationsData } = useGetConversations(
    {},
    {
      enabled: !!currentUser && hasHydrated,
    }
  );

  // Get notifications and unread count
  const { data: notificationsData } = useGetNotifications(
    { page: 1, limit: 10 },
    {
      enabled: !!currentUser && hasHydrated,
    }
  );

  const { data: unreadNotificationsCountData } = useGetUnreadNotificationsCount({
    enabled: !!currentUser && hasHydrated,
  });

  // Get notification store state
  const { notifications, unreadCount, markAsRead, markAllAsRead } = useNotificationStore();

  // Mutation for marking all notifications as read
  const { mutate: markAllNotificationsAsReadMutation } = useMarkAllNotificationsAsRead({
    onSuccess: () => {
      // Use socket event for real-time updates
      markAllNotificationsAsReadSocket().catch((error) => {
        console.error("Failed to emit mark all notifications as read socket event:", error);
      });
    },
  });

  // Calculate unread message count
  useEffect(() => {
    if (conversationsData?.data?.conversations) {
      const totalUnread = conversationsData.data.conversations.reduce(
        (total, conversation) => total + (conversation.unreadCount || 0),
        0
      );
      setUnreadMessageCount(totalUnread);
    }
  }, [conversationsData]);

  // Sync notification store with API data
  useEffect(() => {
    if (notificationsData?.data?.notifications) {
      // Update store with API notifications
      const { setNotifications } = useNotificationStore.getState();
      setNotifications(notificationsData.data.notifications);
    }
  }, [notificationsData]);

  // Sync unread count with API data
  useEffect(() => {
    if (unreadNotificationsCountData?.data?.count !== undefined) {
      // Update store with API unread count
      const { setUnreadCount } = useNotificationStore.getState();
      setUnreadCount(unreadNotificationsCountData.data.count);
    }
  }, [unreadNotificationsCountData]);

  const { mutate: logout } = useLogout({
    onSuccess: () => {
      clearStore();
      queryClient.clear();
      toast.success("Logged out successfully", {
        description: "You have been logged out of your account.",
      });

      setTimeout(() => {
        hideLoader();
        router.push("/login");
      }, 1500);
    },
    onError: (error) => {
      hideLoader();
      toast.error("Logout failed", {
        description: error.response?.data?.message || "Something went wrong. Please try again.",
      });
    },
  });

  // Now we can safely do conditional returns
  if (!hasHydrated) {
    return <FullPageLoader message="Loading..." />;
  }

  const handleLogout = async () => {
    showLoader("Logging you out...");
    await logout();
    const token = localStorage.getItem("fcmToken");
    if (token) await removeFirebaseToken({ token });
  };

  const handlePurchasePro = () => {
    setIsPurchaseProModalOpen(true);
  };

  // Handle notification click
  const handleNotificationClick = async (notificationId: string) => {
    try {
      // Mark as read in store
      markAsRead(notificationId);

      // Use socket event for real-time updates
      await markNotificationAsReadSocket(notificationId);
    } catch (error) {
      console.error("Failed to mark notification as read:", error);
    }
  };

  // Handle mark all notifications as read
  const handleMarkAllAsRead = () => {
    // Mark all as read in store
    markAllAsRead();

    // Use mutation and socket event
    markAllNotificationsAsReadMutation();
  };

  const confirmPurchasePro = () => {
    purchasePro(
      { weeks: selectedWeeks },
      {
        onSuccess: (data: IPurchaseProResponseDto) => {
          setIsPurchaseProModalOpen(false);
          setSelectedWeeks(4); // Reset selected weeks
          if (data?.data?.url) {
            window.open(data.data.url, "_blank");
          }
        },
        onError: (error: ApiError) => {
          toast.error(error.response?.data?.message || "Failed to purchase pro membership");
        },
      }
    );
  };

  const cancelPurchasePro = () => {
    setIsPurchaseProModalOpen(false);
    setSelectedWeeks(4); // Reset selected weeks
  };

  const user = userData?.data as CurrentJobSeekerType | CurrentCompanyType | undefined;
  const isLoggedIn = !!currentUser;
  const isJobseeker = user?.role === UserRole.JOBSEEKER;
  const isRecruiter = user?.role === UserRole.RECRUITER;

  console.log("[Header] User State:", {
    userData: userData?.data,
    currentUser,
    user,
    isLoggedIn,
    isJobseeker,
    isRecruiter,
    userRole: user?.role,
  });

  // Common navigation links for all users
  const commonLinks = [
    { href: "/about-us", label: "About Us" },
    { href: "/how-it-works", label: "How It Works" },
    { href: "/contact", label: "Contact" },
  ];

  // Role-specific navigation links
  const roleSpecificLinks = [
    ...((!isLoggedIn || isJobseeker) && !isRecruiter
      ? [
          { href: "/for-candidates", label: "For Candidates" },
          { href: "/all-companies", label: "All Companies" },
        ]
      : []),
    ...((!isLoggedIn || isRecruiter) && !isJobseeker
      ? [{ href: "/for-recruiters", label: "For Recruiters" }]
      : []),
  ];

  // Dashboard navigation links when in dashboard context
  const dashboardLinks = [
    {
      href: isJobseeker ? "/dashboard/applied-jobs" : "/company-dashboard/all-jobs",
      label: "Jobs",
    },
    ...commonLinks,
  ];

  const mainNavLinks = dashboardPages
    ? [...dashboardLinks, ...roleSpecificLinks]
    : [...roleSpecificLinks, ...commonLinks];
  console.log({ mainNavLinks });
  const endsAt = user?.proMembershipEndsAt || user?.proTrialEndsAt;
  const isProMember = endsAt ? new Date(endsAt) > new Date() : false;

  // console.log(isProMember, "isProMemberisProMemberisProMember");
  return (
    <>
      <>
        {adminSettingsData?.data.TrialEndsAt &&
          new Date(adminSettingsData.data.TrialEndsAt) > new Date() &&
          userData?.data.role === "RECRUITER" && (
            <>
              <div className="relative overflow-hidden bg-orange-100 text-white border-b border-yellow-300 text-center">
                🚀 Trial running! <span className=" font-semibold">Feature your job</span> to get 3x
                more exposure.
              </div>
            </>
          )}
        {adminSettingsData?.data.TrialEndsAt &&
          new Date(adminSettingsData.data.TrialEndsAt) > new Date() &&
          userData?.data.role === "JOBSEEKER" &&
          !isProMember && (
            <div className="relative overflow-hidden bg-orange-100 text-white border-b border-yellow-300 text-center">
              🎉 Trial’s on! Feature your profile and get noticed faster.
            </div>
          )}
      </>

      <header className="sticky top-0 z-50 w-full bg-white">
        <div className="container flex h-16 items-center justify-between mx-auto">
          <Link
            href={
              isLoggedIn
                ? isJobseeker
                  ? "/dashboard/applied-jobs"
                  : "/company-dashboard/all-jobs"
                : "/"
            }
            className="flex items-center space-x-2"
          >
            <Image
              src="/images/logo.png"
              alt="Jobs Logo"
              width={100}
              height={40}
              className="h-10 w-auto"
            />
          </Link>
          <div className="lg:hidden">
            <button
              onClick={() => setIsDrawerOpen(!isDrawerOpen)}
              className="text-gray-800 focus:outline-none"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 6h16M4 12h16m-7 6h7"
                ></path>
              </svg>
            </button>
          </div>
          <nav className="hidden lg:flex items-center space-x-6">
            {mainNavLinks.map((link) => (
              <NavLink key={link.href} href={link.href}>
                {link.label}
              </NavLink>
            ))}
          </nav>
          <div className="hidden lg:flex items-center space-x-4">
            {user ? (
              <>
                {user?.role === "JOBSEEKER" && (
                  <>
                    {(() => {
                      return isProMember ? (
                        <div className="w-[48px] h-[48px] rounded-full inline-flex justify-center items-center bg-blue-300 text-sm text-blue-100 font-bold">
                          <CrownIcon />
                        </div>
                      ) : (
                        <Button
                          variant="default"
                          className="rounded-full gap-x-2 bg-blue-300 px-3 py-2 text-sm text-blue-100 font-bold"
                          onClick={handlePurchasePro}
                          disabled={isPurchasingPro}
                        >
                          <CrownIcon /> Get Featured
                        </Button>
                      );
                    })()}
                  </>
                )}

                <Link
                  href="/message"
                  className="bg-black-100 text-white w-[48px] h-[48px] rounded-full inline-flex justify-center items-center relative"
                >
                  {unreadMessageCount > 0 && (
                    <span className="absolute top-0 right-0 w-4 h-4 bg-red-500 text-white rounded-full flex items-center justify-center text-xs">
                      {unreadMessageCount > 9 ? "9+" : unreadMessageCount}
                    </span>
                  )}
                  <MessageIcon />
                </Link>
                <DropdownMenu
                  open={isNotificationDropdownOpen}
                  onOpenChange={setIsNotificationDropdownOpen}
                >
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="default"
                      className="w-[48px] h-[48px] rounded-full px-0 py-0 relative"
                    >
                      {unreadCount > 0 && (
                        <span className="absolute top-0 right-0 w-4 h-4 bg-red-500 text-white rounded-full flex items-center justify-center text-xs">
                          {unreadCount > 9 ? "9+" : unreadCount}
                        </span>
                      )}
                      <BellIconNotification />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-80 max-h-96 overflow-y-auto">
                    {notifications.length > 0 ? (
                      <>
                        <div className="flex items-center justify-between p-2 border-b">
                          <span className="font-semibold text-sm">Notifications</span>
                          {unreadCount > 0 && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={handleMarkAllAsRead}
                              className="text-xs"
                            >
                              Mark all as read
                            </Button>
                          )}
                        </div>
                        {notifications.slice(0, 10).map((notification) => (
                          <DropdownMenuItem
                            key={notification._id}
                            className={`p-3 cursor-pointer ${
                              !notification.isRead ? "bg-blue-50" : ""
                            }`}
                            onClick={() => handleNotificationClick(notification._id)}
                          >
                            <div className="flex flex-col space-y-1">
                              <div className="flex items-center justify-between">
                                <span className="font-medium text-sm">{notification.title}</span>
                                {!notification.isRead && (
                                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                                )}
                              </div>
                              <span className="text-xs text-gray-600">{notification.message}</span>
                              <span className="text-xs text-gray-600">
                                {new Date(notification.createdAt).toLocaleDateString()}
                              </span>
                            </div>
                          </DropdownMenuItem>
                        ))}
                      </>
                    ) : (
                      <DropdownMenuItem disabled>
                        <span className="text-gray-500">No notifications</span>
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="default" className="w-[48px] h-[48px] rounded-full px-0 py-0">
                      {/* {isRecruiter
                    ? (user as CurrentCompanyType).companyName
                    : `${(user as CurrentJobSeekerType).firstName} ${
                        (user as CurrentJobSeekerType).lastName
                      }`} */}

                      <Image
                        src={user?.profilePicture || DEFAULT_IMAGE}
                        alt="User Profile"
                        width={48}
                        height={48}
                        className="w-[48px] h-[48px] rounded-full object-cover"
                      />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={handleLogout}>Logout</DropdownMenuItem>
                    {isJobseeker && (
                      <>
                        <DropdownMenuItem onClick={() => router.push("/settings/my-profile")}>
                          Settings
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => router.push("/dashboard/applied-jobs")}>
                          Dashboard
                        </DropdownMenuItem>
                      </>
                    )}
                    {isRecruiter && (
                      <>
                        <DropdownMenuItem
                          onClick={() => router.push("/company-settings/company-profile")}
                        >
                          Company Settings
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => router.push("/company-dashboard/all-jobs")}
                        >
                          Dashboard
                        </DropdownMenuItem>
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            ) : (
              <>
                <Link href="/login">
                  <Button
                    variant="outline"
                    className="rounded-full border border-blue-100 text-blue-100 px-5"
                  >
                    Login
                  </Button>
                </Link>
                <Link href="/sign-up">
                  <Button className="rounded-full px-5 bg-orange-100 hover:bg-orange-100 text-white">
                    Sign Up
                  </Button>
                </Link>
              </>
            )}
          </div>
        </div>
        <div
          className={`fixed inset-0 z-40 bg-black bg-opacity-50 transition-opacity ${
            isDrawerOpen ? "opacity-100" : "opacity-0 pointer-events-none"
          }`}
          onClick={() => setIsDrawerOpen(false)}
        ></div>
        <div
          className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform ${
            isDrawerOpen ? "translate-x-0" : "-translate-x-full"
          }`}
        >
          <div className="flex items-center justify-between p-4 border-b">
            <Link
              href={isLoggedIn ? (isJobseeker ? "/for-candidate" : "/for-recruiter") : "/"}
              className="flex items-center space-x-2"
            >
              <Image
                src="/images/logo.png"
                alt="Jobs Logo"
                width={100}
                height={40}
                className="h-10 w-auto"
              />
            </Link>
            <button
              onClick={() => setIsDrawerOpen(false)}
              className="text-gray-800 focus:outline-none"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                ></path>
              </svg>
            </button>
          </div>
          <nav className="flex flex-col p-4 space-y-4">
            {mainNavLinks.map((link) => (
              <NavLink key={link.href} href={link.href}>
                {link.label}
              </NavLink>
            ))}
            {user ? (
              <>
                <span className="text-gray-800">
                  Welcome,{" "}
                  {isRecruiter
                    ? (user as CurrentCompanyType).companyName
                    : `${(user as CurrentJobSeekerType).firstName} ${
                        (user as CurrentJobSeekerType).lastName
                      }`}
                </span>
                <Link
                  href="/message"
                  className="flex items-center gap-2 text-orange-100 font-medium py-2"
                >
                  <MessageIcon />
                  <span>Messages</span>
                  {unreadMessageCount > 0 && (
                    <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1">
                      {unreadMessageCount > 9 ? "9+" : unreadMessageCount}
                    </span>
                  )}
                </Link>
                {isJobseeker && (
                  <Button
                    variant="outline"
                    className="rounded-full border border-blue-100 text-blue-100 px-5"
                    onClick={() => router.push("/settings/my-profile")}
                  >
                    Settings
                  </Button>
                )}
                {isRecruiter && (
                  <Button
                    variant="outline"
                    className="rounded-full border border-blue-100 text-blue-100 px-5"
                    onClick={() => router.push("/company-settings/company-profile")}
                  >
                    Company Settings
                  </Button>
                )}
                <Button
                  variant="outline"
                  className="rounded-full border border-blue-100 text-blue-100 px-5"
                  onClick={handleLogout}
                >
                  Logout
                </Button>
              </>
            ) : (
              <>
                <Link href="/login">
                  <Button
                    variant="outline"
                    className="rounded-full border border-blue-100 text-blue-100 px-5"
                  >
                    Login
                  </Button>
                </Link>
                <Link href="/sign-up">
                  <Button className="rounded-full px-5 bg-orange-100 hover:bg-orange-100 text-white">
                    Sign Up
                  </Button>
                </Link>
              </>
            )}
          </nav>
        </div>
      </header>

      <Dialog open={isPurchaseProModalOpen} onOpenChange={setIsPurchaseProModalOpen}>
        <DialogContent className="w-full max-w-2xl p-0">
          <DialogHeader className="p-6 pb-0">
            <DialogTitle className="text-xl font-semibold">
              Become a Pro Member and unlock exclusive features!
            </DialogTitle>
            <DialogDescription className="text-gray-700">
              Current price per week: ${proMemberPricePerWeek}
            </DialogDescription>
          </DialogHeader>
          <div className="p-6 pt-0">
            <div className="mb-6">
              <label htmlFor="weeks" className="block text-sm font-medium text-gray-700 mb-2">
                Select duration:
              </label>
              <select
                id="weeks"
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base sm:text-sm rounded-md border-orange-100 border text-orange-100 h-[44px] cursor-pointer"
                value={selectedWeeks}
                onChange={(e) => setSelectedWeeks(Number(e.target.value))}
              >
                <option value={1}>1 Week</option>
                <option value={2}>2 Weeks</option>
                <option value={3}>3 Weeks</option>
                <option value={4}>4 Weeks</option>
              </select>
            </div>
            <p className="text-lg font-bold mb-6">Total Price: ${purchaseProPrice}</p>
          </div>
          <DialogFooter className="p-6 pt-0 flex justify-end gap-4">
            <button
              onClick={cancelPurchasePro}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
            >
              Cancel
            </button>
            <button
              onClick={confirmPurchasePro}
              className="px-4 py-2 bg-orange-100 text-white rounded-md hover:bg-orange-200"
              disabled={isPurchasingPro}
            >
              {isPurchasingPro ? "Processing..." : "Confirm Purchase"}
            </button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default Header;
