"use client";

import React from "react";
import PrimaryButton from "../Buttons/PrimaryButton";
import CustomTag from "../CustomTag";
import { PrimaryHeading } from "../Headings/PrimaryHeading";
import { ArrowIcon } from "../Icons";
import JobListingHome from "./JobListingHome";
import SearchBar from "./SearchBar";
import { useGetDiscoverSection } from "@/hooks/useQuery";

const JobsSection = () => {
  const { data: discoverSectionData } = useGetDiscoverSection();

  return (
    <section className="bg-offWhite-100 lg:py-28 py-14">
      <div className="container mx-auto">
        <div className="max-w-[1140px] mx-auto">
          <div className="text-center mb-12 lg:w-[80%] mx-auto">
            <CustomTag>{discoverSectionData?.data.section.subheading}</CustomTag>
            <PrimaryHeading className="mt-14">
              {/* Discover Opportunities That Bring You <br /> Closer to Your <span>Dream Career</span> */}
              {discoverSectionData?.data.section.heading}
            </PrimaryHeading>
            <p className="text-gray-100 mt-8">{discoverSectionData?.data.section.description}</p>
          </div>
          <SearchBar redirectTo="/for-candidates" />
        </div>
        <JobListingHome />
        <div className="text-center lg:mt-20 mt-10">
          <PrimaryButton link="/for-candidate" text="See More" icon={<ArrowIcon />} />
        </div>
      </div>
    </section>
  );
};

export default JobsSection;
