"use client";

import { useQueryClient } from "@tanstack/react-query";
import { useCallback, useEffect, useRef } from "react";
import { Socket } from "socket.io-client";
import { toast } from "sonner";
import { useHydrateChatStore } from "@/hooks/useHydrateChatStore";
import { useHydrateNotificationStore } from "@/hooks/useHydrateNotificationStore";
import { debugLog, debugError } from "@/lib/debug";
import {
  disconnectSocket,
  initializeSocket,
  SocketConnectionStatus,
  getSocketConnectionStatus,
  debugSocketState,
  joinConversation,
  SocketNotificationEvent,
  SocketNotificationReadEvent,
  SocketAllNotificationsReadEvent,
} from "@/service/socket.service";
import { useChatStore } from "@/store/useChatStore";
import { useNotificationStore } from "@/store/useNotificationStore";
import { useUserStore } from "@/store/useUserStore";
import { IMessage, INotification } from "@/types/query.types";

interface SocketProviderProps {
  children: React.ReactNode;
}

/**
 * SocketProvider component that manages socket connection and event listeners
 * This component should be placed high in the component tree to provide socket
 * functionality to all child components
 */
export default function SocketProvider({ children }: SocketProviderProps) {
  const { currentUser, token } = useUserStore();
  const { setTyping, setSocketStatus } = useChatStore();
  const queryClient = useQueryClient();

  // Hydrate the chat store
  const storeHydrated = useHydrateChatStore();

  // Hydrate the notification store
  const notificationStoreHydrated = useHydrateNotificationStore();

  // Use refs to store intervals so they can be properly cleaned up
  const pingIntervalRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const reconnectIntervalRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const socketCheckIntervalRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const socketRef = useRef<Socket | null>(null);

  // Debug function to log socket state
  const logSocketState = () => {
    const state = debugSocketState();
    debugLog("Socket state:", state);
    return state;
  };

  // Setup socket event listeners
  const setupSocketListeners = useCallback(
    (socket: Socket) => {
      // Handle auto-join conversations
      socket.on("join_conversation", (data) => {
        if (data.autoJoined) {
          debugLog(`Automatically joined ${data.conversationCount} conversations`);
        } else {
          debugLog(`Joined conversation: ${data.conversationId}`);
        }

        // Invalidate conversations query to ensure the UI is updated
        // queryClient.invalidateQueries({ queryKey: ["get-conversations"] });

        // If we have a specific conversation ID, also invalidate that conversation's messages
        // if (data.conversationId) {
        //   queryClient.invalidateQueries({
        //     queryKey: ["get-messages", data.conversationId],
        //   });
        //   queryClient.invalidateQueries({
        //     queryKey: ["get-messages-infinite", data.conversationId],
        //   });
        // }
      });

      // Handle send_message event
      socket.on("send_message", (data) => {
        debugLog(`Message sent via socket event: ${JSON.stringify(data)}`);
        // This event is just for acknowledgment, no need to refetch
        // The actual message data will come through the new_message event
      });

      // Handle new conversation
      socket.on("create_conversation", (data) => {
        debugLog(`New conversation created: ${data.conversationId}`);

        // Invalidate conversations query to ensure the UI is updated
        // queryClient.invalidateQueries({ queryKey: ["get-conversations"] });

        // After a conversation is created, automatically join it
        if (data.conversationId) {
          try {
            joinConversation(data.conversationId);
            debugLog(`Auto-joining newly created conversation: ${data.conversationId}`);
          } catch (error) {
            debugError(`Failed to auto-join conversation: ${data.conversationId}`, error);
          }
        }
      });

      // Handle new message
      socket.on("new_message", (message: IMessage) => {
        debugLog("Real-time message received in SocketProvider:", message);

        // Get the store methods in a way that doesn't cause hydration issues
        const { addMessage, updateLastMessage, pendingMessages, removePendingMessage } =
          useChatStore.getState();

        // Add to messages array for the conversation
        addMessage(message.conversationId, message);

        // Also update lastMessage for the conversation list
        updateLastMessage(message.conversationId, message);

        debugLog(`Updated store with new message for conversation ${message.conversationId}`);

        // Remove any pending messages that might have been created for this message
        // This helps prevent duplicates if the sender receives their own message via socket
        const matchingPendingMessages = pendingMessages.filter(
          (msg) =>
            msg.conversationId === message.conversationId &&
            msg.content === message.content &&
            new Date(msg.createdAt).getTime() > Date.now() - 30000 // Within last 30 seconds
        );

        if (matchingPendingMessages.length > 0) {
          debugLog(
            `Found ${matchingPendingMessages.length} matching pending messages, removing them`
          );
          matchingPendingMessages.forEach((msg) => {
            removePendingMessage(msg.id);
          });
        }

        // Update the cache directly for immediate UI update without triggering API calls
        const updateCache = (oldData: unknown) => {
          if (!oldData) return oldData;

          // For regular query data structure
          if (oldData && typeof oldData === "object" && "data" in oldData) {
            const typedData = oldData as { data: { messages: IMessage[] } };
            return {
              ...oldData,
              data: {
                ...typedData.data,
                messages: [...typedData.data.messages, message],
              },
            };
          }

          // For infinite query data structure
          if (
            oldData &&
            typeof oldData === "object" &&
            "pages" in oldData &&
            Array.isArray(oldData.pages) &&
            oldData.pages.length > 0
          ) {
            return {
              ...oldData,
              pages: oldData.pages.map((page, i) => {
                // Add the new message to the first page
                if (i === 0 && typeof page === "object" && page !== null && "data" in page) {
                  const typedPage = page as { data: { messages: IMessage[] } };
                  return {
                    ...page,
                    data: {
                      ...typedPage.data,
                      messages: [...typedPage.data.messages, message],
                    },
                  };
                }
                return page;
              }),
            };
          }

          return oldData;
        };

        // Update the cache directly without triggering API calls
        queryClient.setQueryData(["get-messages", message.conversationId], updateCache);
        queryClient.setQueryData(["get-messages-infinite", message.conversationId], updateCache);

        // No need to invalidate queries since we're using the store
        // The UI will update automatically through the store subscription
      });

      // Handle message read
      socket.on("message_read", (data) => {
        debugLog("Message read event received:", data);

        // Update message status in store
        const { updateMessage } = useChatStore.getState();
        updateMessage(data.conversationId, data.messageId, { read: true });

        // Update the cache directly for immediate UI update without triggering API calls
        const updateMessageInCache = (oldData: unknown) => {
          if (!oldData) return oldData;

          // For regular query data structure
          if (oldData && typeof oldData === "object" && "data" in oldData) {
            const typedData = oldData as { data: { messages: IMessage[] } };
            return {
              ...oldData,
              data: {
                ...typedData.data,
                messages: typedData.data.messages.map((msg) =>
                  msg._id === data.messageId ? { ...msg, read: true } : msg
                ),
              },
            };
          }

          // For infinite query data structure
          if (
            oldData &&
            typeof oldData === "object" &&
            "pages" in oldData &&
            Array.isArray(oldData.pages)
          ) {
            return {
              ...oldData,
              pages: oldData.pages.map((page) => {
                if (typeof page === "object" && page !== null && "data" in page) {
                  const typedPage = page as { data: { messages: IMessage[] } };
                  return {
                    ...page,
                    data: {
                      ...typedPage.data,
                      messages: typedPage.data.messages.map((msg) =>
                        msg._id === data.messageId ? { ...msg, read: true } : msg
                      ),
                    },
                  };
                }
                return page;
              }),
            };
          }

          return oldData;
        };

        // Update the cache directly without triggering API calls
        if (data.conversationId) {
          queryClient.setQueryData(["get-messages", data.conversationId], updateMessageInCache);
          queryClient.setQueryData(
            ["get-messages-infinite", data.conversationId],
            updateMessageInCache
          );
        }

        // No need to invalidate queries
      });

      // Handle typing indicator
      socket.on("typing", (data) => {
        debugLog(`${data.userName} is typing in conversation ${data.conversationId}...`);
        // Get store state and methods in a way that doesn't cause hydration issues
        const { activeConversationId, setTyping } = useChatStore.getState();

        // Only set typing status if this is the active conversation
        if (activeConversationId === data.conversationId) {
          setTyping(data.conversationId, true);
        }
      });

      // Handle typing stop
      socket.on("stop_typing", (data) => {
        debugLog(`${data.userName} stopped typing in conversation ${data.conversationId}`);
        // Get store state and methods in a way that doesn't cause hydration issues
        const { activeConversationId, setTyping } = useChatStore.getState();

        // Only clear typing status if this is the active conversation
        if (activeConversationId === data.conversationId) {
          setTyping(data.conversationId, false);
        }
      });

      // Handle message deleted
      // Note: We've added a reusable message_deleted listener in socket.service.ts
      // The useDeleteMessage hook now uses listenForMessageDeleted to handle message deletion
      // This ensures consistent handling of message deletion events
      // We still keep this global listener for real-time updates from other users
      socket.on(
        "message_deleted",
        (data: {
          messageId: string;
          conversationId: string;
          userId?: string;
          deleteForEveryone?: boolean;
        }) => {
          debugLog("Message deleted event received:", data);

          // Get store state and methods in a way that doesn't cause hydration issues
          const { messages, lastMessages, updateMessage, updateLastMessage } =
            useChatStore.getState();

          // First, find the message in the store
          const conversationMessages = messages[data.conversationId] || [];
          const messageIndex = conversationMessages.findIndex((msg) => msg._id === data.messageId);

          if (messageIndex !== -1) {
            const message = conversationMessages[messageIndex];

            if (message) {
              // Update the message based on deletion type
              if (data.deleteForEveryone) {
                // For deleteForEveryone, update the message content
                updateMessage(data.conversationId, data.messageId, {
                  content: "This message has been deleted",
                  deletedForEveryone: true,
                  updatedAt: new Date().toISOString(),
                });
              } else if (data.userId) {
                // For deleteForMe, update the deletedBy array
                const deletedBy = [...(message.deletedBy || [])];
                if (!deletedBy.includes(data.userId)) {
                  deletedBy.push(data.userId);
                }
                updateMessage(data.conversationId, data.messageId, {
                  deletedBy,
                });
              }

              // Check if this is the last message in the conversation
              const lastMessage = lastMessages[data.conversationId];
              if (lastMessage && lastMessage._id === data.messageId) {
                // Update the last message in the conversation
                const updatedMessage = conversationMessages[messageIndex];
                if (updatedMessage) {
                  updateLastMessage(data.conversationId, updatedMessage);
                }
              }
            }
          }

          // Update the cache directly for immediate UI update without triggering API calls
          const updateMessageInCache = (oldData: unknown) => {
            if (!oldData) return oldData;

            // For regular query data structure
            if (oldData && typeof oldData === "object" && "data" in oldData) {
              const typedData = oldData as { data: { messages: IMessage[] } };

              // Update messages based on deletion type
              const updatedMessages = typedData.data.messages.map((msg) => {
                if (msg._id !== data.messageId) return msg;

                // Handle deleteForEveryone case
                if (data.deleteForEveryone) {
                  return {
                    ...msg,
                    content: "This message has been deleted",
                    deletedForEveryone: true,
                    updatedAt: new Date().toISOString(),
                  };
                }
                // Handle deleteForMe case
                else if (data.userId) {
                  const deletedBy = [...(msg.deletedBy || [])];
                  if (!deletedBy.includes(data.userId)) {
                    deletedBy.push(data.userId);
                  }
                  return {
                    ...msg,
                    deletedBy,
                  };
                }

                return msg;
              });

              return {
                ...oldData,
                data: {
                  ...typedData.data,
                  messages: updatedMessages,
                },
              };
            }

            // For infinite query data structure
            if (
              oldData &&
              typeof oldData === "object" &&
              "pages" in oldData &&
              Array.isArray(oldData.pages)
            ) {
              return {
                ...oldData,
                pages: oldData.pages.map((page) => {
                  if (typeof page === "object" && page !== null && "data" in page) {
                    const typedPage = page as { data: { messages: IMessage[] } };

                    // Update messages based on deletion type
                    const updatedMessages = typedPage.data.messages.map((msg) => {
                      if (msg._id !== data.messageId) return msg;

                      // Handle deleteForEveryone case
                      if (data.deleteForEveryone) {
                        return {
                          ...msg,
                          content: "This message has been deleted",
                          deletedForEveryone: true,
                          updatedAt: new Date().toISOString(),
                        };
                      }
                      // Handle deleteForMe case
                      else if (data.userId) {
                        const deletedBy = [...(msg.deletedBy || [])];
                        if (!deletedBy.includes(data.userId)) {
                          deletedBy.push(data.userId);
                        }
                        return {
                          ...msg,
                          deletedBy,
                        };
                      }

                      return msg;
                    });

                    return {
                      ...page,
                      data: {
                        ...typedPage.data,
                        messages: updatedMessages,
                      },
                    };
                  }
                  return page;
                }),
              };
            }

            return oldData;
          };

          // Update the cache directly without triggering API calls
          if (data.conversationId) {
            queryClient.setQueryData(["get-messages", data.conversationId], updateMessageInCache);
            queryClient.setQueryData(
              ["get-messages-infinite", data.conversationId],
              updateMessageInCache
            );
          }
        }
      );

      // Handle conversation deleted
      socket.on("conversation_deleted", (data) => {
        debugLog("Conversation deleted event received:", data);

        // Get store methods in a way that doesn't cause hydration issues
        const { deleteConversation } = useChatStore.getState();

        // Update store
        deleteConversation(data.conversationId);

        // We still need to invalidate conversations list for this specific event
        // since deleting a conversation is a less frequent operation
        queryClient.invalidateQueries({ queryKey: ["get-conversations"] });
      });

      // Handle new notification
      socket.on("new_notification", (notificationData: SocketNotificationEvent) => {
        debugLog("New notification received:", notificationData);

        // Get notification store methods
        const { addNotification } = useNotificationStore.getState();

        // Convert socket notification to INotification format
        const notification: INotification = {
          _id: notificationData.notificationId,
          userId: notificationData.userId,
          title: notificationData.title,
          message: notificationData.message,
          type: notificationData.type,
          isRead: notificationData.isRead,
          metadata: notificationData.metadata,
          expiresAt: undefined,
          createdAt: notificationData.createdAt,
          updatedAt: notificationData.createdAt,
          __v: 0,
        };

        // Add notification to store
        addNotification(notification);

        // Show toast notification
        toast.success(notification.title, {
          description: notification.message,
        });

        // Invalidate notification queries to keep them in sync
        queryClient.invalidateQueries({ queryKey: ["get-notifications"] });
        queryClient.invalidateQueries({ queryKey: ["get-unread-notifications-count"] });
      });

      // Handle notification read
      socket.on("notification_read", (data: SocketNotificationReadEvent) => {
        debugLog("Notification read event received:", data);

        // Get notification store methods
        const { markAsRead } = useNotificationStore.getState();

        // Mark notification as read in store
        markAsRead(data.notificationId);

        // Invalidate notification queries to keep them in sync
        queryClient.invalidateQueries({ queryKey: ["get-notifications"] });
        queryClient.invalidateQueries({ queryKey: ["get-unread-notifications-count"] });
      });

      // Handle all notifications read
      socket.on("all_notifications_read", (data: SocketAllNotificationsReadEvent) => {
        debugLog("All notifications read event received:", data);

        // Get notification store methods
        const { markAllAsRead } = useNotificationStore.getState();

        // Mark all notifications as read in store
        markAllAsRead();

        // Invalidate notification queries to keep them in sync
        queryClient.invalidateQueries({ queryKey: ["get-notifications"] });
        queryClient.invalidateQueries({ queryKey: ["get-unread-notifications-count"] });
      });

      // Handle connection status changes
      socket.on("connect", () => {
        debugLog("Socket connected");
        setSocketStatus(SocketConnectionStatus.CONNECTED);
        // Log socket state on connect
        logSocketState();
      });

      socket.on("connect_error", (error) => {
        debugError("Socket connection error:", error);
        setSocketStatus(SocketConnectionStatus.ERROR);
      });

      socket.on("disconnect", (reason) => {
        debugLog(`Socket disconnected: ${reason}`);
        setSocketStatus(SocketConnectionStatus.DISCONNECTED);
      });
    },
    [queryClient, setSocketStatus]
  );

  useEffect(() => {
    // Check if token and user are available and stores are hydrated
    if (!token || !currentUser || !storeHydrated || !notificationStoreHydrated) {
      debugLog("No token, user, or stores not hydrated yet - not initializing socket");
      setSocketStatus(SocketConnectionStatus.DISCONNECTED);
      return;
    }

    // Log token length for debugging
    debugLog(`Initializing socket with token of length: ${token.length}`);
    if (process.env.NODE_ENV === "development") {
      debugLog(`Token starts with: ${token.substring(0, 10)}...`);
    }

    // Initialize socket with token
    try {
      const socket = initializeSocket(token);
      socketRef.current = socket;

      if (!socket) {
        debugError("Failed to initialize socket - socket is null");
        setSocketStatus(SocketConnectionStatus.ERROR);
        return;
      }

      // Log socket connection details
      debugLog("Socket initialized with details:", {
        id: socket.id,
        connected: socket.connected,
        disconnected: socket.disconnected,
      });

      // Update socket status in store
      setSocketStatus(getSocketConnectionStatus());

      // Force reconnect if not connected
      if (!socket.connected) {
        debugLog("Socket not connected after initialization, forcing reconnect");
        socket.connect();
      }

      // Set up socket event listeners
      setupSocketListeners(socket);

      // Set up a periodic check to ensure socket is connected
      socketCheckIntervalRef.current = setInterval(() => {
        if (socketRef.current && !socketRef.current.connected) {
          debugLog("Socket disconnected during periodic check, attempting to reconnect");
          socketRef.current.connect();
        }
      }, 10000); // Check every 10 seconds

      // Add a ping interval to keep the connection alive
      pingIntervalRef.current = setInterval(() => {
        if (socketRef.current && socketRef.current.connected) {
          socketRef.current.emit("ping");
          // Log socket state periodically for debugging
          if (process.env.NODE_ENV === "development") {
            logSocketState();
          }
        } else {
          // Update socket status if disconnected
          setSocketStatus(getSocketConnectionStatus());
        }
      }, 30000); // Ping every 30 seconds

      // Set up reconnect interval if socket disconnects
      socket.on("disconnect", () => {
        // Try to reconnect immediately
        if (reconnectIntervalRef.current) {
          clearInterval(reconnectIntervalRef.current);
        }

        reconnectIntervalRef.current = setInterval(() => {
          if (!socketRef.current || !socketRef.current.connected) {
            try {
              debugLog("Attempting to reconnect socket...");
              if (token) {
                const newSocket = initializeSocket(token);
                socketRef.current = newSocket;

                if (newSocket && newSocket.connected) {
                  debugLog("Socket reconnected successfully");
                  setSocketStatus(SocketConnectionStatus.CONNECTED);

                  // Set up socket event listeners again
                  setupSocketListeners(newSocket);

                  if (reconnectIntervalRef.current) {
                    clearInterval(reconnectIntervalRef.current);
                    reconnectIntervalRef.current = undefined;
                  }

                  // Log socket state on reconnect
                  logSocketState();
                }
              }
            } catch (error) {
              debugError("Failed to reconnect socket:", error);
              setSocketStatus(SocketConnectionStatus.ERROR);
            }
          } else {
            if (reconnectIntervalRef.current) {
              clearInterval(reconnectIntervalRef.current);
              reconnectIntervalRef.current = undefined;
            }
          }
        }, 5000); // Try to reconnect every 5 seconds
      });
    } catch (error) {
      debugError("Error initializing socket:", error);
      setSocketStatus(SocketConnectionStatus.ERROR);
    }

    // Clean up function
    return () => {
      // Clear all intervals
      if (pingIntervalRef.current) {
        clearInterval(pingIntervalRef.current);
        pingIntervalRef.current = undefined;
      }

      if (reconnectIntervalRef.current) {
        clearInterval(reconnectIntervalRef.current);
        reconnectIntervalRef.current = undefined;
      }

      if (socketCheckIntervalRef.current) {
        clearInterval(socketCheckIntervalRef.current);
        socketCheckIntervalRef.current = undefined;
      }

      // Disconnect socket
      if (socketRef.current) {
        debugLog("Disconnecting socket on cleanup");
        disconnectSocket();
        socketRef.current = null;
      }
    };
  }, [
    token,
    currentUser,
    storeHydrated,
    notificationStoreHydrated,
    queryClient,
    setSocketStatus,
    setTyping,
    setupSocketListeners,
  ]);

  return <>{children}</>;
}
