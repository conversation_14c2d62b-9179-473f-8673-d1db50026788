"use client";
import Image from "next/image";
import React from "react";
import { PrimaryHeading } from "../Headings/PrimaryHeading";
import { useGetPartnersSection } from "@/hooks/useQuery";

// interface ICompanyLogo {
//   id: number;
//   src: string;
//   name: string;
// }

// const CompaniesLogos: ICompanyLogo[] = [
//   {
//     id: 1,
//     src: "/images/logo1.png",
//     name: "HAYS",
//   },
//   {
//     id: 2,
//     src: "/images/logo2.png",
//     name: "<PERSON>",
//   },
//   {
//     id: 3,
//     src: "/images/logo3.png",
//     name: "<PERSON>",
//   },
//   {
//     id: 4,
//     src: "/images/logo4.png",
//     name: "robet walters",
//   },
//   {
//     id: 5,
//     src: "/images/logo5.png",
//     name: "adecco",
//   },
//   {
//     id: 6,
//     src: "/images/logo6.png",
//     name: "randstad",
//   },
// ];

const SapportCompaniesSection = () => {
  const { data: partnersSectionData } = useGetPartnersSection();

  return (
    <section className="relative lg:pt-28 lg:pb-36 pb-14">
      <div className="absolute w-full top-0 left-0 z-[-1]">
        <Image alt="" src={"/images/spiral.png"} width={837} height={1920} className="w-full" />
      </div>
      <div className="container mx-auto">
        <div className="grid lg:grid-cols-2 lg:gap-x-20 items-center">
          <div>
            <PrimaryHeading>
              {/* Proudly Supported by <span>YesJobs</span> */}
              {partnersSectionData?.data.section.heading}
            </PrimaryHeading>
            <p className="text-gray-100 mt-4 ">{partnersSectionData?.data.section.description}</p>
          </div>
          <div className="grid grid-cols-2 gap-y-4 lg:w-[90%] ml-auto mt-6 lg:mt-0">
            {partnersSectionData?.data.section.partners.map((company) => (
              <div key={company._id} className="flex items-center justify-center">
                <Image
                  src={company.imageURL}
                  alt={company.name}
                  width={304}
                  height={76}
                  className="mx-auto"
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default SapportCompaniesSection;
