import { useInfiniteQuery, useQuery, UseQueryOptions } from "@tanstack/react-query";
import {
  getAllEnums,
  getAllJobs,
  getAllJobSeekers,
  getAllCompanies,
  getCompanyProfile,
  getCompanyProfileById,
  getConversationById,
  getConversations,
  getCurrentUser,
  getJobApplicants,
  getJobById,
  getJobSeekerProfile,
  getMessages,
  getMyJobApplications,
  getMyShortlistedApplications,
  getProfileById,
  getRecentJobs,
  getRecruitersJobs,
  getRelatedCandidates,
  getRelatedCompanies,
  getSavedCandidates,
  getSavedJobs,
  getShortlistedApplicants,
  getAllApplicants,
  getAdminSettings,
  getPayments,
  getNotifications,
  getUnreadNotificationsCount,
  getFirebaseTokens,
  getOpportunitySection,
  getTalentSection,
  getDiscoverSection,
  getCompaniesSection,
  getPartnersSection,
  getCtaSection,
  getAppSection,
  getAboutUsPage,
  getHowItWorksPage,
  getContactUsPage,
  getTermsAndConditionsPage,
  getPrivacyPolicyPage,
} from "@/service/query.service";
import { ApiError } from "@/types/common.types";
import { IAdminSettingsResponseDto } from "@/types/mutation.types";
import {
  IGetCompanyProfileResponseDto,
  IGetCurrentUserResponse,
  IGetEnumsResponseDto,
  IGetJobSeekerProfileResponseDto,
  IGetProfileByIdResponseDto,
  ICreateJobResponseDto,
  IGetRecruiterJobResponseDto,
  IGetRecentJobsResponseDto,
  IJobSearchParams,
  IGetAllJobsResponseDto,
  IJobSeekerSearchParams,
  IGetAllJobSeekersResponseDto,
  IGetJobApplicationsResponseDto,
  IGetSavedCandidatesResponseDto,
  IGetJobApplicantsResponseDto,
  IGetAllCompaniesResponseDto,
  IGetSavedJobsResponseDto,
  IShortlistedApplicantsSearchParams,
  IGetShortlistedApplicantsResponseDto,
  IAllApplicantsSearchParams,
  IGetAllApplicantsResponseDto,
  IGetConversationsResponseDto,
  IGetConversationResponseDto,
  IGetMessagesResponseDto,
  IRelatedCompaniesParams,
  IGetRelatedCompaniesResponseDto,
  IRelatedCandidatesParams,
  IGetRelatedCandidatesResponseDto,
  IGetPaymentsResponseDto,
  IGetNotificationsResponseDto,
  IGetNotificationsParams,
  IGetUnreadNotificationsCountResponseDto,
  IGetFirebaseTokenResponseDto,
  IGetOpportunitySectionResponseDto,
  IGetTalentSectionResponseDto,
  IGetDiscoverSectionResponseDto,
  IGetCompaniesSectionResponseDto,
  IGetPartnersSectionResponseDto,
  IGetCtaSectionResponseDto,
  IGetAppSectionResponseDto,
  IGetAboutUsPageResponseDto,
  IGetHowItWorksPageResponseDto,
  IGetContactUsPageResponseDto,
  IGetTermsAndConditionsPageResponseDto,
  IGetPrivacyPolicyPageResponseDto,
} from "@/types/query.types";

export function useGetCurrentUser(
  options?: Omit<
    UseQueryOptions<IGetCurrentUserResponse, ApiError, IGetCurrentUserResponse>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetCurrentUserResponse, ApiError>({
    queryKey: ["get-current-user"],
    queryFn: getCurrentUser,
    ...options,
  });
}

export function useGetContactUsPage(
  options?: Omit<
    UseQueryOptions<IGetContactUsPageResponseDto, ApiError, IGetContactUsPageResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetContactUsPageResponseDto, ApiError>({
    queryKey: ["get-contact-us-page"],
    queryFn: getContactUsPage,
    ...options,
  });
}

export function useGetTermsAndConditionsPage(
  options?: Omit<
    UseQueryOptions<
      IGetTermsAndConditionsPageResponseDto,
      ApiError,
      IGetTermsAndConditionsPageResponseDto
    >,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetTermsAndConditionsPageResponseDto, ApiError>({
    queryKey: ["get-terms-and-conditions-page"],
    queryFn: getTermsAndConditionsPage,
    ...options,
  });
}

export function useGetPrivacyPolicyPage(
  options?: Omit<
    UseQueryOptions<IGetPrivacyPolicyPageResponseDto, ApiError, IGetPrivacyPolicyPageResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetPrivacyPolicyPageResponseDto, ApiError>({
    queryKey: ["get-privacy-policy-page"],
    queryFn: getPrivacyPolicyPage,
    ...options,
  });
}

export function useGetHowItWorksPage(
  options?: Omit<
    UseQueryOptions<IGetHowItWorksPageResponseDto, ApiError, IGetHowItWorksPageResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetHowItWorksPageResponseDto, ApiError>({
    queryKey: ["get-how-it-works-page"],
    queryFn: getHowItWorksPage,
    ...options,
  });
}

export function useGetAboutUsPage(
  options?: Omit<
    UseQueryOptions<IGetAboutUsPageResponseDto, ApiError, IGetAboutUsPageResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetAboutUsPageResponseDto, ApiError>({
    queryKey: ["get-about-us-page"],
    queryFn: getAboutUsPage,
    ...options,
  });
}

export function useGetCtaSection(
  options?: Omit<
    UseQueryOptions<IGetCtaSectionResponseDto, ApiError, IGetCtaSectionResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetCtaSectionResponseDto, ApiError>({
    queryKey: ["get-cta-section"],
    queryFn: getCtaSection,
    ...options,
  });
}

export function useGetAppSection(
  options?: Omit<
    UseQueryOptions<IGetAppSectionResponseDto, ApiError, IGetAppSectionResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetAppSectionResponseDto, ApiError>({
    queryKey: ["get-app-section"],
    queryFn: getAppSection,
    ...options,
  });
}

export function useGetOpportunitySection(
  options?: Omit<
    UseQueryOptions<IGetOpportunitySectionResponseDto, ApiError, IGetOpportunitySectionResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetOpportunitySectionResponseDto, ApiError>({
    queryKey: ["get-opportunity-section"],
    queryFn: getOpportunitySection,
    ...options,
  });
}

export function useGetTalentSection(
  options?: Omit<
    UseQueryOptions<IGetTalentSectionResponseDto, ApiError, IGetTalentSectionResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetTalentSectionResponseDto, ApiError>({
    queryKey: ["get-talent-section"],
    queryFn: getTalentSection,
    ...options,
  });
}

export function useGetDiscoverSection(
  options?: Omit<
    UseQueryOptions<IGetDiscoverSectionResponseDto, ApiError, IGetDiscoverSectionResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetDiscoverSectionResponseDto, ApiError>({
    queryKey: ["get-discover-section"],
    queryFn: getDiscoverSection,
    ...options,
  });
}

export function useGetCompaniesSection(
  options?: Omit<
    UseQueryOptions<IGetCompaniesSectionResponseDto, ApiError, IGetCompaniesSectionResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetCompaniesSectionResponseDto, ApiError>({
    queryKey: ["get-companies-section"],
    queryFn: getCompaniesSection,
    ...options,
  });
}

export function useGetPartnersSection(
  options?: Omit<
    UseQueryOptions<IGetPartnersSectionResponseDto, ApiError, IGetPartnersSectionResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetPartnersSectionResponseDto, ApiError>({
    queryKey: ["get-partners-section"],
    queryFn: getPartnersSection,
    ...options,
  });
}

export function useGetFirebaseTokens(
  options?: Omit<
    UseQueryOptions<IGetFirebaseTokenResponseDto, ApiError, IGetFirebaseTokenResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetFirebaseTokenResponseDto, ApiError>({
    queryKey: ["get-firebase-tokens"],
    queryFn: getFirebaseTokens,
    ...options,
  });
}

export function useGetUnreadNotificationsCount(
  options?: Omit<
    UseQueryOptions<
      IGetUnreadNotificationsCountResponseDto,
      ApiError,
      IGetUnreadNotificationsCountResponseDto
    >,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetUnreadNotificationsCountResponseDto, ApiError>({
    queryKey: ["get-unread-notifications-count"],
    queryFn: getUnreadNotificationsCount,
    ...options,
  });
}

export function useGetNotifications(
  params?: IGetNotificationsParams,
  options?: Omit<
    UseQueryOptions<IGetNotificationsResponseDto, ApiError, IGetNotificationsResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetNotificationsResponseDto, ApiError>({
    queryKey: ["get-notifications", params],
    queryFn: () => getNotifications(params),
    ...options,
  });
}

export function useGetPayments(
  params?: {
    page?: number | string;
    limit?: number | string;
  },
  options?: Omit<
    UseQueryOptions<IGetPaymentsResponseDto, ApiError, IGetPaymentsResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetPaymentsResponseDto, ApiError>({
    queryKey: ["get-payments", params],
    queryFn: () => getPayments(params),
    ...options,
  });
}

export function useGetJobSeekerProfile(
  options?: Omit<
    UseQueryOptions<IGetJobSeekerProfileResponseDto, ApiError, IGetJobSeekerProfileResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetJobSeekerProfileResponseDto, ApiError>({
    queryKey: ["get-jobseeker-profile"],
    queryFn: getJobSeekerProfile,
    ...options,
  });
}
export function useGetCompanyProfile(
  options?: Omit<
    UseQueryOptions<IGetCompanyProfileResponseDto, ApiError, IGetCompanyProfileResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetCompanyProfileResponseDto, ApiError>({
    queryKey: ["get-company-profile"],
    queryFn: getCompanyProfile,
    ...options,
  });
}

export function useGetAllEnums(
  options?: Omit<UseQueryOptions<IGetEnumsResponseDto, ApiError>, "queryKey" | "queryFn">
) {
  return useQuery<IGetEnumsResponseDto, ApiError>({
    queryKey: ["get-all-enums"],
    queryFn: getAllEnums,
    ...options,
  });
}

export function useGetJobById(
  id: string,
  options?: Omit<UseQueryOptions<ICreateJobResponseDto, ApiError>, "queryKey" | "queryFn">
) {
  return useQuery<ICreateJobResponseDto, ApiError>({
    queryKey: ["get-job-by-id", id], // Include `id` in the queryKey
    queryFn: () => getJobById(id), // Pass `id` to the query function
    ...options,
  });
}

export function useGetProfileById(
  id: string,
  options?: Omit<UseQueryOptions<IGetProfileByIdResponseDto, ApiError>, "queryKey" | "queryFn">
) {
  return useQuery<IGetProfileByIdResponseDto, ApiError>({
    queryKey: ["get-profile-by-id", id], // Include `id` in the queryKey
    queryFn: () => getProfileById(id), // Pass `id` to the query function
    ...options,
  });
}

export function useGetRecruiterJobs(
  params?: {
    page?: number;
    limit?: number;
  },
  options?: Omit<
    UseQueryOptions<IGetRecruiterJobResponseDto, ApiError, IGetRecruiterJobResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetRecruiterJobResponseDto, ApiError>({
    queryKey: ["get-recruiter-jobs", params],
    queryFn: () => getRecruitersJobs(params),
    ...options,
  });
}

export function useGetRecentJobs(
  params?: {
    page?: number;
    limit?: number;
  },
  options?: Omit<
    UseQueryOptions<IGetRecentJobsResponseDto, ApiError, IGetRecentJobsResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetRecentJobsResponseDto, ApiError>({
    queryKey: ["get-recent-jobs", params],
    queryFn: () => getRecentJobs(params),
    ...options,
  });
}

export function useGetAllJobs(
  params?: IJobSearchParams,
  options?: Omit<
    UseQueryOptions<IGetAllJobsResponseDto, ApiError, IGetAllJobsResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetAllJobsResponseDto, ApiError>({
    queryKey: ["get-all-jobs", params],
    queryFn: () => getAllJobs(params),
    ...options,
  });
}

export function useGetAllJobSeekers(
  params?: IJobSeekerSearchParams,
  options?: Omit<
    UseQueryOptions<IGetAllJobSeekersResponseDto, ApiError, IGetAllJobSeekersResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetAllJobSeekersResponseDto, ApiError>({
    queryKey: ["get-all-jobseekers", params],
    queryFn: () => getAllJobSeekers(params),
    ...options,
  });
}

export function useGetMyJobApplications(
  params?: {
    page?: number;
    limit?: number;
  },
  options?: Omit<
    UseQueryOptions<IGetJobApplicationsResponseDto, ApiError, IGetJobApplicationsResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetJobApplicationsResponseDto, ApiError>({
    queryKey: ["get-my-job-applications", params],
    queryFn: () => getMyJobApplications(params),
    ...options,
  });
}

export function useGetMyShortlistedApplications(
  params?: {
    page?: number;
    limit?: number;
  },
  options?: Omit<
    UseQueryOptions<IGetJobApplicationsResponseDto, ApiError, IGetJobApplicationsResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetJobApplicationsResponseDto, ApiError>({
    queryKey: ["get-my-shortlisted-applications", params],
    queryFn: () => getMyShortlistedApplications(params),
    ...options,
  });
}

export function useGetSavedCandidates(
  params?: {
    page?: number;
    limit?: number;
  },
  options?: Omit<
    UseQueryOptions<IGetSavedCandidatesResponseDto, ApiError, IGetSavedCandidatesResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetSavedCandidatesResponseDto, ApiError>({
    queryKey: ["get-saved-candidates"],
    queryFn: () => getSavedCandidates(params),
    ...options,
  });
}

export function useGetJobApplicants(
  jobId: string,
  options?: Omit<
    UseQueryOptions<IGetJobApplicantsResponseDto, ApiError, IGetJobApplicantsResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetJobApplicantsResponseDto, ApiError>({
    queryKey: ["get-job-applicants", jobId],
    queryFn: () => getJobApplicants(jobId),
    // enabled: !!jobId,
    ...options,
  });
}

export function useGetAllCompanies(
  params?: {
    page?: number;
    limit?: number;
    search?: string;
    coordinates?: [number, number];
  },
  options?: Omit<
    UseQueryOptions<IGetAllCompaniesResponseDto, ApiError, IGetAllCompaniesResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetAllCompaniesResponseDto, ApiError>({
    queryKey: ["get-all-companies", params],
    queryFn: () => getAllCompanies(params),
    ...options,
  });
}
export function useGetSavedJobs(
  params?: {
    page?: number;
    limit?: number;
  },
  options?: Omit<
    UseQueryOptions<IGetSavedJobsResponseDto, ApiError, IGetSavedJobsResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetSavedJobsResponseDto, ApiError>({
    queryKey: ["get-saved-jobs", params],
    queryFn: () => getSavedJobs(params),
    ...options,
  });
}

export function useGetCompanyProfileById(
  id: string,
  options?: Omit<
    UseQueryOptions<IGetCompanyProfileResponseDto, ApiError, IGetCompanyProfileResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetCompanyProfileResponseDto, ApiError>({
    queryKey: ["get-company-profile-by-id", id],
    queryFn: () => getCompanyProfileById(id),
    enabled: !!id,
    ...options,
  });
}

export function useGetShortlistedApplicants(
  params?: IShortlistedApplicantsSearchParams,
  options?: Omit<
    UseQueryOptions<
      IGetShortlistedApplicantsResponseDto,
      ApiError,
      IGetShortlistedApplicantsResponseDto
    >,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetShortlistedApplicantsResponseDto, ApiError>({
    queryKey: ["get-shortlisted-applicants", params],
    queryFn: () => getShortlistedApplicants(params),
    ...options,
  });
}

export function useGetAllApplicants(
  params?: IAllApplicantsSearchParams,
  options?: Omit<
    UseQueryOptions<IGetAllApplicantsResponseDto, ApiError, IGetAllApplicantsResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetAllApplicantsResponseDto, ApiError>({
    queryKey: ["get-all-applicants", params],
    queryFn: () => getAllApplicants(params),
    ...options,
  });
}

export function useGetConversations(
  params?: {
    page?: number;
    limit?: number;
  },
  options?: Omit<
    UseQueryOptions<IGetConversationsResponseDto, ApiError, IGetConversationsResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  // Get the current path to determine if we should enable the query
  const pathname = typeof window !== "undefined" ? window.location.pathname : "";

  // Enable the query if we're on the messages page, regardless of URL parameters
  const enabled = pathname === "/message";

  return useQuery<IGetConversationsResponseDto, ApiError>({
    queryKey: ["get-conversations", params],
    queryFn: () => getConversations(params),
    enabled,
    // Reduce refetching frequency - only refetch on window focus or manual triggers
    // This prevents unnecessary API calls since we're using the store for real-time updates
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    refetchOnReconnect: false,
    staleTime: 2 * 60 * 1000, // Consider data fresh for 2 minutes
    ...options,
  });
}

export function useGetConversationById(
  conversationId: string,
  options?: Omit<
    UseQueryOptions<IGetConversationResponseDto, ApiError, IGetConversationResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  // Get the current path to determine if we should enable the query
  const pathname = typeof window !== "undefined" ? window.location.pathname : "";
  const searchParams =
    typeof window !== "undefined" ? new URLSearchParams(window.location.search) : null;
  const currentConversationId = searchParams?.get("id");

  // Only enable the query if we're on the conversation page and the IDs match
  const enabled = pathname === "/message" && currentConversationId === conversationId;

  return useQuery<IGetConversationResponseDto, ApiError>({
    queryKey: ["get-conversation-by-id", conversationId],
    queryFn: () => getConversationById(conversationId),
    enabled,
    // Reduce refetching frequency - only refetch on window focus or manual triggers
    // This prevents unnecessary API calls since we're using the store for real-time updates
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    refetchOnReconnect: false,
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    ...options,
  });
}

export function useGetMessages(
  params: {
    conversationId: string;
    page?: number;
    limit?: number;
  },
  options?: Omit<
    UseQueryOptions<IGetMessagesResponseDto, ApiError, IGetMessagesResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  // Get the current path to determine if we should enable the query
  const pathname = typeof window !== "undefined" ? window.location.pathname : "";
  const searchParams =
    typeof window !== "undefined" ? new URLSearchParams(window.location.search) : null;
  const currentConversationId = searchParams?.get("id");

  // Only enable the query if we're on the conversation page and the IDs match
  const enabled = pathname === "/message" && currentConversationId === params.conversationId;

  return useQuery<IGetMessagesResponseDto, ApiError>({
    queryKey: ["get-messages", params.conversationId, params.page],
    queryFn: () => getMessages(params),
    enabled,
    // Reduce refetching frequency - only refetch on window focus or manual triggers
    // This prevents unnecessary API calls since we're using the store for real-time updates
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    refetchOnReconnect: false,
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    ...options,
  });
}

export function useGetMessagesInfinite(params: { conversationId: string; limit?: number }) {
  // Get the current path to determine if we should enable the query
  const pathname = typeof window !== "undefined" ? window.location.pathname : "";
  const searchParams =
    typeof window !== "undefined" ? new URLSearchParams(window.location.search) : null;
  const currentConversationId = searchParams?.get("id");

  // Only enable the query if we're on the conversation page and the IDs match
  const enabled = pathname === "/message" && currentConversationId === params.conversationId;

  return useInfiniteQuery<IGetMessagesResponseDto, ApiError>({
    queryKey: ["get-messages-infinite", params.conversationId],
    queryFn: ({ pageParam }) => {
      // Ensure pageParam is a number
      const page = typeof pageParam === "number" ? pageParam : 1;
      return getMessages({
        conversationId: params.conversationId,
        page,
        limit: params.limit,
      });
    },
    initialPageParam: 1, // Add this required property for TanStack Query v5
    getNextPageParam: (lastPage) => {
      const { pagination } = lastPage.data;
      return pagination.hasNextPage ? pagination.currentPage + 1 : undefined;
    },
    enabled,
    // Reduce refetching frequency - only refetch on window focus or manual triggers
    // This prevents unnecessary API calls since we're using the store for real-time updates
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    refetchOnReconnect: false,
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
  });
}

export function useGetRelatedCompanies(
  companyProfileId: string,
  params?: IRelatedCompaniesParams,
  options?: Omit<
    UseQueryOptions<IGetRelatedCompaniesResponseDto, ApiError, IGetRelatedCompaniesResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetRelatedCompaniesResponseDto, ApiError>({
    queryKey: ["get-related-companies", companyProfileId, params],
    queryFn: () => getRelatedCompanies(companyProfileId, params),
    enabled: !!companyProfileId,
    ...options,
  });
}

export function useGetRelatedCandidates(
  jobSeekerProfileId: string,
  params?: IRelatedCandidatesParams,
  options?: Omit<
    UseQueryOptions<IGetRelatedCandidatesResponseDto, ApiError, IGetRelatedCandidatesResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IGetRelatedCandidatesResponseDto, ApiError>({
    queryKey: ["get-related-candidates", jobSeekerProfileId, params],
    queryFn: () => getRelatedCandidates(jobSeekerProfileId, params),
    enabled: !!jobSeekerProfileId,
    ...options,
  });
}

export function useGetAdminSettings(
  options?: Omit<
    UseQueryOptions<IAdminSettingsResponseDto, ApiError, IAdminSettingsResponseDto>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery<IAdminSettingsResponseDto, ApiError>({
    queryKey: ["get-admin-settings"],
    queryFn: getAdminSettings,
    ...options,
  });
}
