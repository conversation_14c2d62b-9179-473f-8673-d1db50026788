"use client";

import Image from "next/image";
import React from "react";
import PrimaryButton from "../Buttons/PrimaryButton";
import OrangeHeading from "../Headings/OrangeHeading";
import { useGetCtaSection } from "@/hooks/useQuery";

const CTASection = () => {
  const { data: ctaSectionData } = useGetCtaSection();
  return (
    <section>
      <div className="container mx-auto bg-offWhite-100 rounded-3xl">
        <div className="flex justify-between items-center py-5 lg:px-10 px-3">
          <div className="lg:max-w-[590px]">
            <OrangeHeading text={ctaSectionData?.data.section.heading || ""} />
            <p className="text-gray-100 mt-4 mb-6">{ctaSectionData?.data.section.description}</p>
            <PrimaryButton link="#" text="Register Account" />
          </div>
          <div className="hidden lg:block">
            <Image
              src={ctaSectionData?.data.section.image || "/images/card.png"}
              alt=""
              width={388}
              height={376}
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
