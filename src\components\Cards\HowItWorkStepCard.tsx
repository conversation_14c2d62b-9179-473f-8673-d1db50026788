import React from "react";

interface HowItWorkStepCardProps {
  countTitle: number;
  title: string;
  text: string;
}

const HowItWorkStepCard: React.FC<HowItWorkStepCardProps> = ({ countTitle, title, text }) => {
  return (
    <div className="p-8 rounded-2xl border border-gray-300 hover:border-orange-100">
      <div className="w-12 h-12 bg-orange-100 text-white flex items-center justify-center rounded-full mb-4 text-2xl">
        {countTitle}
      </div>
      <div className="font-bold text-2xl text-black-100 mb-2">{title}</div>
      <div className="text-gray-100 text-lg leading-7 font-normal">{text}</div>
    </div>
  );
};

export default HowItWorkStepCard;
