"use client";

import { useSearchParams } from "next/navigation";
import { useEffect, useState, Suspense } from "react";

import CandidateCard from "@/components/Cards/CandidateCard";
import { BannerHeading } from "@/components/Headings/BannerHeading";
import LoadingSpinner from "@/components/LoadingSpinner/LoadingSpinner";
import Pagination from "@/components/Pagination/Pagination";
import SearchBar from "@/components/Sections/SearchBar";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { useGetAllCompanies } from "@/hooks/useQuery";
import { useUserStore } from "@/store/useUserStore";

export default function AllCompaniesListing() {
  const { currentUser } = useUserStore();
  const searchParams = useSearchParams();
  const [searchState, setSearchState] = useState({
    search: searchParams.get("search") || "",
    coordinates: undefined as [number, number] | undefined,
  });
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);

  const { data, isLoading, isError, refetch } = useGetAllCompanies(
    {
      page,
      limit,
      search: searchState.search,
      coordinates: searchState.coordinates,
    },
    { refetchOnWindowFocus: false }
  );

  const companies = data?.data?.allCompanies || [];
  const pagination = data?.data?.pagination;

  useEffect(() => {
    refetch();
  }, [page, limit, refetch]);

  const handleSearch = (params: { search: string; coordinates?: [number, number] }) => {
    setSearchState({
      search: params.search,
      coordinates: params.coordinates,
    });
    setPage(1); // Reset to first page on new search
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleLimitChange = (newLimit: string) => {
    setLimit(Number(newLimit));
    setPage(1); // Reset to first page when changing items per page
  };

  return (
    <>
      <section className="bg-offWhite-100 py-20">
        <div className="container mx-auto">
          <div className="mb-10">
            <BannerHeading>
              Find the Talent That <span>Drives Success</span>
            </BannerHeading>
          </div>
          <div className="mb-10">
            <Suspense fallback={<div>Loading search...</div>}>
              <SearchBar
                onSearch={handleSearch}
                initialSearch={searchState.search}
                initialLocation={searchParams.get("location") || ""}
              />
            </Suspense>
          </div>
        </div>
      </section>
      <section className="pt-16 pb-20">
        <div className="container mx-auto">
          <div className="flex justify-between items-center">
            <div className="w-full">
              {pagination && (
                <p className="text-[#000]">
                  Showing {pagination.skip + 1} -{" "}
                  {Math.min(pagination.skip + pagination.pageSize, pagination.totalRecords)} of{" "}
                  {pagination.totalRecords} results
                </p>
              )}
            </div>
            <div>
              <Select onValueChange={handleLimitChange} defaultValue={String(limit)}>
                <SelectTrigger className="w-full bg-white rounded-full h-[56px] px-6 text-gray-100 text-base">
                  <SelectValue placeholder={`${limit} Per Page`} />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="10">10 Per Page</SelectItem>
                    <SelectItem value="20">20 Per Page</SelectItem>
                    <SelectItem value="30">30 Per Page</SelectItem>
                    <SelectItem value="40">40 Per Page</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <LoadingSpinner />
            </div>
          ) : isError ? (
            <div className="text-center py-8 text-red-500">
              Error loading companies. Please try again later.
            </div>
          ) : companies.length === 0 ? (
            <div className="text-center py-8">
              No companies found matching your search criteria.
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-10">
              {companies.map((company) => (
                <CandidateCard
                  key={company._id}
                  isAllCompanies
                  candidateId={company._id}
                  candidateDescription={
                    company?.aboutCompany?.description || "No description available."
                  }
                  candidateImage={company.companyProfile.profilePicture || DEFAULT_IMAGE}
                  candidateLocation={
                    company.companyProfile.location
                      ? `${company.companyProfile.location.formattedAddress}`
                      : "Location not specified"
                  }
                  // candidateLocation={
                  //   company.companyProfile.location
                  //     ? `${company.companyProfile.location.formattedAddress}`
                  //     : "Location not specified"
                  // }
                  candidateName={company.companyProfile.companyName}
                  link={currentUser ? `/all-companies/${company._id}` : `/login`}
                  openJobs={company.activeJobs} // This would need to be updated if the API provides this information
                />
              ))}
            </div>
          )}

          {pagination && pagination.pages > 1 && (
            <div className="flex justify-center mt-8">
              <Pagination
                currentPage={page}
                totalPages={pagination.pages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </div>
      </section>
    </>
  );
}
